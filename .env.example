# AI合同审查系统 - 环境变量配置模板

# ===========================================
# 基础应用配置
# ===========================================
PROJECT_NAME=AI合同审查系统
VERSION=1.0.0
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=your-secret-key-change-in-production

# ===========================================
# 数据库配置
# ===========================================
# PostgreSQL主数据库
DATABASE_URL=postgresql://contract_user:contract_pass@localhost:5432/contract_db

# Redis缓存数据库
REDIS_URL=redis://localhost:6379/0

# ===========================================
# AI模型配置
# ===========================================
# OpenAI配置（开发环境）
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_BASE_URL=https://api.openai.com/v1

# 本地模型配置（生产环境）
LOCAL_MODEL_ENABLED=false
LOCAL_MODEL_URL=http://localhost:11434

# 千问模型配置（推荐）
QWEN_MODEL_7B=qwen2.5:7b
QWEN_MODEL_14B=qwen2.5:14b
QWEN_MODEL_32B=qwen2.5:32b

# Llama模型配置
LLAMA_MODEL_8B=llama3:8b
LLAMA_MODEL_70B=llama3:70b

# 模型任务分配
MODEL_SUMMARY=qwen2.5:7b
MODEL_RISK=qwen2.5:14b
MODEL_CLAUSE=qwen2.5:7b
MODEL_COMPLIANCE=qwen2.5:14b

# 嵌入模型配置
EMBEDDING_MODEL=qwen2-embeddings
EMBEDDING_DIMENSIONS=1536
EMBEDDING_BATCH_SIZE=100

# ===========================================
# 向量数据库配置
# ===========================================
CHROMA_HOST=localhost
CHROMA_PORT=8001
CHROMA_COLLECTION_NAME=contract_knowledge

# ===========================================
# 文件上传配置
# ===========================================
UPLOAD_DIR=uploads
MAX_FILE_SIZE=52428800
ALLOWED_FILE_TYPES=.pdf,.docx,.doc,.txt

# ===========================================
# 安全配置
# ===========================================
ACCESS_TOKEN_EXPIRE_MINUTES=10080
ALLOWED_HOSTS=["*"]

# ===========================================
# 分析配置
# ===========================================
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_TOKENS=4000

# 风险评估阈值
RISK_THRESHOLD_HIGH=0.8
RISK_THRESHOLD_MEDIUM=0.5

# ===========================================
# 缓存配置
# ===========================================
CACHE_TTL=3600

# ===========================================
# 日志配置
# ===========================================
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# ===========================================
# 前端配置
# ===========================================
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_TITLE=AI合同审查系统
