"""
文档处理服务 - 负责文档解析、文本提取和预处理
"""
import fitz  # PyMuPDF
from docx import Document as DocxDocument
import pdfplumber
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from loguru import logger
import re
from dataclasses import dataclass

from app.core.config import settings


@dataclass
class DocumentMetadata:
    """文档元数据"""
    title: Optional[str] = None
    author: Optional[str] = None
    subject: Optional[str] = None
    creator: Optional[str] = None
    creation_date: Optional[str] = None
    modification_date: Optional[str] = None
    page_count: int = 0
    word_count: int = 0


@dataclass
class ExtractedContent:
    """提取的文档内容"""
    text: str
    metadata: DocumentMetadata
    tables: List[Dict] = None
    images: List[Dict] = None
    structure: Dict = None


class DocumentProcessor:
    """文档处理器"""
    
    def __init__(self):
        self.supported_formats = {'.pdf', '.docx', '.doc', '.txt'}
    
    async def process_document(self, file_path: Path) -> ExtractedContent:
        """
        处理文档并提取内容
        
        Args:
            file_path: 文档文件路径
            
        Returns:
            ExtractedContent: 提取的内容和元数据
        """
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        file_extension = file_path.suffix.lower()
        
        if file_extension not in self.supported_formats:
            raise ValueError(f"不支持的文件格式: {file_extension}")
        
        logger.info(f"开始处理文档: {file_path}")
        
        try:
            if file_extension == '.pdf':
                return await self._process_pdf(file_path)
            elif file_extension in ['.docx', '.doc']:
                return await self._process_docx(file_path)
            elif file_extension == '.txt':
                return await self._process_txt(file_path)
            else:
                raise ValueError(f"未实现的文件格式处理: {file_extension}")
                
        except Exception as e:
            logger.error(f"文档处理失败: {file_path}, 错误: {str(e)}")
            raise
    
    async def _process_pdf(self, file_path: Path) -> ExtractedContent:
        """处理PDF文档"""
        text_content = ""
        tables = []
        metadata = DocumentMetadata()
        
        # 使用PyMuPDF提取文本和元数据
        with fitz.open(file_path) as doc:
            # 提取元数据
            meta = doc.metadata
            metadata.title = meta.get('title', '')
            metadata.author = meta.get('author', '')
            metadata.subject = meta.get('subject', '')
            metadata.creator = meta.get('creator', '')
            metadata.creation_date = meta.get('creationDate', '')
            metadata.modification_date = meta.get('modDate', '')
            metadata.page_count = doc.page_count
            
            # 提取文本内容
            for page_num in range(doc.page_count):
                page = doc[page_num]
                text_content += page.get_text()
                text_content += "\n\n"
        
        # 使用pdfplumber提取表格
        try:
            with pdfplumber.open(file_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    page_tables = page.extract_tables()
                    if page_tables:
                        for table_idx, table in enumerate(page_tables):
                            tables.append({
                                'page': page_num + 1,
                                'table_index': table_idx,
                                'data': table,
                                'headers': table[0] if table else []
                            })
        except Exception as e:
            logger.warning(f"表格提取失败: {e}")
        
        # 清理文本
        text_content = self._clean_text(text_content)
        metadata.word_count = len(text_content.split())
        
        return ExtractedContent(
            text=text_content,
            metadata=metadata,
            tables=tables
        )
    
    async def _process_docx(self, file_path: Path) -> ExtractedContent:
        """处理DOCX文档"""
        doc = DocxDocument(file_path)
        
        # 提取文本内容
        text_content = ""
        for paragraph in doc.paragraphs:
            text_content += paragraph.text + "\n"
        
        # 提取表格
        tables = []
        for table_idx, table in enumerate(doc.tables):
            table_data = []
            for row in table.rows:
                row_data = [cell.text.strip() for cell in row.cells]
                table_data.append(row_data)
            
            tables.append({
                'table_index': table_idx,
                'data': table_data,
                'headers': table_data[0] if table_data else []
            })
        
        # 提取元数据
        core_props = doc.core_properties
        metadata = DocumentMetadata(
            title=core_props.title or "",
            author=core_props.author or "",
            subject=core_props.subject or "",
            creator=core_props.creator or "",
            creation_date=str(core_props.created) if core_props.created else "",
            modification_date=str(core_props.modified) if core_props.modified else "",
            page_count=len(doc.paragraphs),  # 近似页数
            word_count=len(text_content.split())
        )
        
        # 清理文本
        text_content = self._clean_text(text_content)
        
        return ExtractedContent(
            text=text_content,
            metadata=metadata,
            tables=tables
        )
    
    async def _process_txt(self, file_path: Path) -> ExtractedContent:
        """处理TXT文档"""
        with open(file_path, 'r', encoding='utf-8') as f:
            text_content = f.read()
        
        # 基本元数据
        metadata = DocumentMetadata(
            title=file_path.stem,
            word_count=len(text_content.split()),
            page_count=1
        )
        
        # 清理文本
        text_content = self._clean_text(text_content)
        
        return ExtractedContent(
            text=text_content,
            metadata=metadata
        )
    
    def _clean_text(self, text: str) -> str:
        """清理和标准化文本"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符但保留标点
        text = re.sub(r'[^\w\s\u4e00-\u9fff.,;:!?()[\]{}"\'-]', ' ', text)
        
        # 标准化换行
        text = re.sub(r'\n+', '\n', text)
        
        return text.strip()
    
    def extract_contract_structure(self, text: str) -> Dict:
        """
        提取合同结构信息
        
        Args:
            text: 合同文本
            
        Returns:
            Dict: 合同结构信息
        """
        structure = {
            'sections': [],
            'clauses': [],
            'parties': [],
            'dates': [],
            'amounts': []
        }
        
        # 提取章节标题
        section_pattern = r'第[一二三四五六七八九十\d]+[章节条款][\s]*([^\n]+)'
        sections = re.findall(section_pattern, text)
        structure['sections'] = sections
        
        # 提取条款
        clause_pattern = r'(\d+\.?\d*[\s]*[^\n]{10,100})'
        clauses = re.findall(clause_pattern, text)
        structure['clauses'] = clauses[:20]  # 限制数量
        
        # 提取当事方
        party_patterns = [
            r'甲方[：:]\s*([^\n，,。.]{2,50})',
            r'乙方[：:]\s*([^\n，,。.]{2,50})',
            r'委托方[：:]\s*([^\n，,。.]{2,50})',
            r'承包方[：:]\s*([^\n，,。.]{2,50})'
        ]
        
        for pattern in party_patterns:
            matches = re.findall(pattern, text)
            structure['parties'].extend(matches)
        
        # 提取日期
        date_pattern = r'(\d{4}年\d{1,2}月\d{1,2}日|\d{4}-\d{1,2}-\d{1,2}|\d{4}/\d{1,2}/\d{1,2})'
        dates = re.findall(date_pattern, text)
        structure['dates'] = list(set(dates))
        
        # 提取金额
        amount_pattern = r'([一二三四五六七八九十百千万亿\d,，.]+[元万千百十])'
        amounts = re.findall(amount_pattern, text)
        structure['amounts'] = list(set(amounts))
        
        return structure
