# 千问模型集成指南

## 1. 概述

千问（Qwen）是阿里云开发的大语言模型系列，在中文理解和生成方面表现优异，特别适合合同审查等中文法律文档处理任务。本指南详细介绍如何在AI合同审查系统中集成和使用千问模型。

## 2. 千问模型系列

### 2.1 可用模型版本

#### 2.1.1 商业版模型（推荐API调用）

| 模型名称 | 上下文长度 | 适用场景 | 输入价格 | 输出价格 |
|---------|-----------|---------|---------|---------|
| qwen-max | 131K tokens | 复杂任务，效果最好 | 0.0024元/千Token | 0.0096元/千Token |
| qwen-plus | 131K tokens | 效果、速度、成本均衡 | 0.0008元/千Token | 0.002元/千Token |
| qwen-flash | 1M tokens | 简单任务，速度快 | 0.00015元/千Token | 0.0015元/千Token |
| qwen-turbo | 131K tokens | 后续不再更新 | 0.0003元/千Token | 0.0006元/千Token |
| qwen-long | 10M tokens | 长文本分析 | 0.0005元/千Token | 0.002元/千Token |

#### 2.1.2 开源版模型（本地部署）

| 模型名称 | 参数规模 | 上下文长度 | 显存需求 | 适用场景 |
|---------|---------|-----------|---------|---------|
| Qwen3-235B | 2350亿 | 131K tokens | 200GB+ | 最强推理能力 |
| Qwen3-32B | 320亿 | 131K tokens | 32GB | 复杂法律推理 |
| Qwen2.5-72B | 720亿 | 131K tokens | 80GB | 专业法律分析 |
| Qwen2.5-32B | 320亿 | 131K tokens | 32GB | 平衡性能 |
| Qwen2.5-14B | 140亿 | 131K tokens | 16GB | 风险评估、合规检查 |
| Qwen2.5-7B | 70亿 | 131K tokens | 8GB | 合同摘要、条款分析 |

### 2.2 模型特点

#### 优势
- **中文优化**: 专门针对中文语言优化，理解准确
- **长上下文**: 支持32K tokens，适合长文档处理
- **法律理解**: 在法律文本理解方面表现优异
- **推理能力**: 强大的逻辑推理和分析能力
- **本地部署**: 支持完全本地化部署

#### 适用场景
- 中文合同文档分析
- 法律条款理解和解释
- 风险识别和评估
- 合规性检查
- 专业术语处理

## 3. 安装和配置

### 3.1 API调用方式（推荐）

#### 3.1.1 获取API Key
```bash
# 1. 注册阿里云账号并开通百炼服务
# 2. 前往API-KEY页面创建API Key
# 3. 配置环境变量
export DASHSCOPE_API_KEY="your-api-key-here"
```

#### 3.1.2 安装SDK
```bash
# Python SDK
pip install dashscope
# 或者使用OpenAI兼容SDK
pip install openai
```

### 3.2 本地部署方式（Ollama）

```bash
# 1. 安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 2. 下载千问模型
ollama pull qwen2.5:7b    # 推荐：日常使用
ollama pull qwen2.5:14b   # 推荐：复杂分析
ollama pull qwen2.5:32b   # 可选：高精度需求

# 3. 启动Ollama服务
ollama serve

# 4. 测试模型
ollama run qwen2.5:7b "你好，请介绍一下你的能力"
```

### 3.2 系统要求

#### 硬件要求
```yaml
Qwen2.5-7B:
  - 显存: 8GB+ (GPU推理)
  - 内存: 16GB+ (CPU推理)
  - 存储: 5GB模型文件

Qwen2.5-14B:
  - 显存: 16GB+ (GPU推理)
  - 内存: 32GB+ (CPU推理)
  - 存储: 9GB模型文件

Qwen2.5-32B:
  - 显存: 32GB+ (GPU推理)
  - 内存: 64GB+ (CPU推理)
  - 存储: 20GB模型文件
```

#### 软件要求
```yaml
操作系统: Linux/macOS/Windows
Python: 3.8+
CUDA: 11.8+ (GPU推理)
Ollama: 0.1.0+
```

## 4. 代码集成

### 4.1 API调用方式

#### 4.1.1 使用DashScope SDK
```python
# config/qwen_api_config.py
import dashscope
from langchain_community.llms import Tongyi
from langchain_community.embeddings import DashScopeEmbeddings

class QwenAPIConfig:
    """千问API配置类"""

    def __init__(self, api_key: str):
        self.api_key = api_key
        dashscope.api_key = api_key

        # 商业版模型配置
        self.models = {
            "max": "qwen-max",           # 最强效果
            "plus": "qwen-plus",         # 均衡选择
            "flash": "qwen-flash",       # 高速低成本
            "long": "qwen-long"          # 长文本
        }

    def get_llm(self, model_type: str = "plus", **kwargs):
        """获取千问语言模型"""
        model_name = self.models.get(model_type, "qwen-plus")

        return Tongyi(
            model_name=model_name,
            temperature=kwargs.get("temperature", 0.1),
            top_p=kwargs.get("top_p", 0.8),
            max_tokens=kwargs.get("max_tokens", 2000)
        )

    def get_embeddings(self):
        """获取千问嵌入模型"""
        return DashScopeEmbeddings(
            model="text-embedding-v3",
            dashscope_api_key=self.api_key
        )
```

#### 4.1.2 使用OpenAI兼容接口
```python
# config/qwen_openai_config.py
from openai import OpenAI

class QwenOpenAIConfig:
    """千问OpenAI兼容配置类"""

    def __init__(self, api_key: str):
        self.client = OpenAI(
            api_key=api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
        )

        self.models = {
            "max": "qwen-max",
            "plus": "qwen-plus",
            "flash": "qwen-flash",
            "long": "qwen-long"
        }

    def chat_completion(self, messages: list, model_type: str = "plus", **kwargs):
        """聊天完成"""
        model_name = self.models.get(model_type, "qwen-plus")

        response = self.client.chat.completions.create(
            model=model_name,
            messages=messages,
            temperature=kwargs.get("temperature", 0.1),
            max_tokens=kwargs.get("max_tokens", 2000),
            stream=kwargs.get("stream", False)
        )

        return response
```

### 4.2 本地部署方式

```python
# config/qwen_local_config.py
from langchain_community.llms import Ollama
from langchain_community.embeddings import OllamaEmbeddings

class QwenLocalConfig:
    """千问本地部署配置类"""

    def __init__(self):
        self.base_url = "http://localhost:11434"
        self.models = {
            "qwen_7b": "qwen2.5:7b",
            "qwen_14b": "qwen2.5:14b",
            "qwen_32b": "qwen2.5:32b"
        }

    def get_llm(self, model_size: str = "7b", **kwargs):
        """获取千问语言模型"""
        model_name = self.models.get(f"qwen_{model_size}", "qwen2.5:7b")

        return Ollama(
            model=model_name,
            base_url=self.base_url,
            temperature=kwargs.get("temperature", 0.1),
            top_p=kwargs.get("top_p", 0.8),
            top_k=kwargs.get("top_k", 40),
            repeat_penalty=kwargs.get("repeat_penalty", 1.1)
        )

    def get_embeddings(self):
        """获取千问嵌入模型"""
        return OllamaEmbeddings(
            model="qwen2-embeddings",
            base_url=self.base_url
        )
```

### 4.2 任务特定配置

```python
# services/qwen_analyzer.py
from typing import Dict, Any
from .qwen_config import QwenConfig

class QwenAnalyzer:
    """基于千问模型的合同分析器"""
    
    def __init__(self):
        self.config = QwenConfig()
        self.task_models = {
            "summary": self.config.get_llm("7b", temperature=0.1),
            "risk": self.config.get_llm("14b", temperature=0.2),
            "clause": self.config.get_llm("7b", temperature=0.1),
            "compliance": self.config.get_llm("14b", temperature=0.1)
        }
    
    async def analyze_contract_summary(self, text: str) -> str:
        """生成合同摘要"""
        prompt = f"""
请为以下合同生成一份简洁的摘要，包括：
1. 合同性质和目的
2. 主要当事方
3. 核心条款
4. 重要时间节点
5. 主要权利义务

合同内容：
{text}

请用专业但易懂的语言，控制在300字以内：
"""
        
        llm = self.task_models["summary"]
        response = await llm.ainvoke(prompt)
        return response
    
    async def assess_contract_risks(self, text: str) -> Dict[str, Any]:
        """评估合同风险"""
        prompt = f"""
请对以下合同进行全面的风险评估，从以下维度分析：

1. 法律风险 - 条款是否符合法律法规
2. 商业风险 - 商业条款是否合理
3. 操作风险 - 执行过程中可能遇到的问题
4. 财务风险 - 付款、担保等财务相关风险
5. 合规风险 - 是否符合行业规范

对每个风险类别，请提供：
- 风险等级（低/中/高/严重）
- 风险描述
- 可能的影响
- 建议的缓解措施

合同内容：
{text}

请以JSON格式返回结果。
"""
        
        llm = self.task_models["risk"]
        response = await llm.ainvoke(prompt)
        
        try:
            import json
            return json.loads(response)
        except json.JSONDecodeError:
            return {"error": "解析风险评估结果失败", "raw_response": response}
```

### 4.3 性能优化配置

```python
# services/qwen_optimizer.py
import asyncio
from concurrent.futures import ThreadPoolExecutor
from typing import List, Dict, Any

class QwenOptimizer:
    """千问模型性能优化器"""
    
    def __init__(self, max_workers: int = 3):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.analyzer = QwenAnalyzer()
    
    async def parallel_analysis(self, text: str) -> Dict[str, Any]:
        """并行执行多个分析任务"""
        
        # 创建分析任务
        tasks = [
            self.analyzer.analyze_contract_summary(text),
            self.analyzer.assess_contract_risks(text),
            self.analyzer.analyze_contract_clauses(text),
            self.analyzer.check_compliance(text)
        ]
        
        # 并行执行
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        return {
            "summary": results[0] if not isinstance(results[0], Exception) else None,
            "risks": results[1] if not isinstance(results[1], Exception) else None,
            "clauses": results[2] if not isinstance(results[2], Exception) else None,
            "compliance": results[3] if not isinstance(results[3], Exception) else None,
            "errors": [str(r) for r in results if isinstance(r, Exception)]
        }
    
    def batch_process_texts(self, texts: List[str]) -> List[Dict[str, Any]]:
        """批量处理文本"""
        
        async def process_batch():
            tasks = [self.parallel_analysis(text) for text in texts]
            return await asyncio.gather(*tasks)
        
        return asyncio.run(process_batch())
```

## 5. 提示工程优化

### 5.1 合同分析专用提示模板

```python
# prompts/qwen_prompts.py
class QwenPrompts:
    """千问模型专用提示模板"""
    
    CONTRACT_SUMMARY_TEMPLATE = """
你是一位专业的法律顾问，请为以下合同生成一份简洁的摘要。

摘要应包括：
1. 合同的主要目的和性质
2. 合同当事方信息
3. 关键条款和义务
4. 重要时间节点
5. 主要权利和责任

请用专业但易懂的语言，控制在300字以内。

合同内容：
{contract_text}

摘要：
"""
    
    RISK_ASSESSMENT_TEMPLATE = """
作为专业的法律风险评估师，请对以下合同进行全面的风险评估。

请从以下维度分析风险：
1. 法律风险 - 条款是否符合法律法规
2. 商业风险 - 商业条款是否合理  
3. 操作风险 - 执行过程中可能遇到的问题
4. 财务风险 - 付款、担保等财务相关风险
5. 合规风险 - 是否符合行业规范

对每个风险类别，请提供：
- 风险等级（低/中/高/严重）
- 风险描述
- 可能的影响
- 建议的缓解措施

合同内容：
{contract_text}

请以JSON格式返回结果：
"""
    
    CLAUSE_ANALYSIS_TEMPLATE = """
请分析以下合同条款，并提供详细的分析结果。

分析要点：
1. 条款类型识别
2. 条款完整性评估
3. 条款清晰度评估
4. 潜在问题识别
5. 改进建议

条款内容：
{clause_text}

分析结果：
"""
```

## 6. 监控和调优

### 6.1 性能监控

```python
# monitoring/qwen_monitor.py
import time
import psutil
from typing import Dict, Any

class QwenMonitor:
    """千问模型性能监控"""
    
    def __init__(self):
        self.metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_response_time": 0,
            "memory_usage": 0,
            "gpu_usage": 0
        }
    
    def track_request(self, func):
        """请求追踪装饰器"""
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            self.metrics["total_requests"] += 1
            
            try:
                result = await func(*args, **kwargs)
                self.metrics["successful_requests"] += 1
                return result
            except Exception as e:
                self.metrics["failed_requests"] += 1
                raise e
            finally:
                end_time = time.time()
                response_time = end_time - start_time
                self.update_average_response_time(response_time)
                self.update_system_metrics()
        
        return wrapper
    
    def update_average_response_time(self, response_time: float):
        """更新平均响应时间"""
        current_avg = self.metrics["average_response_time"]
        total_requests = self.metrics["total_requests"]
        
        new_avg = (current_avg * (total_requests - 1) + response_time) / total_requests
        self.metrics["average_response_time"] = new_avg
    
    def update_system_metrics(self):
        """更新系统指标"""
        self.metrics["memory_usage"] = psutil.virtual_memory().percent
        
        try:
            import GPUtil
            gpus = GPUtil.getGPUs()
            if gpus:
                self.metrics["gpu_usage"] = gpus[0].load * 100
        except ImportError:
            pass
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取监控指标"""
        return self.metrics.copy()
```

## 7. 故障排除

### 7.1 常见问题

#### 问题1：模型加载失败
```bash
# 检查Ollama服务状态
ollama ps

# 重新拉取模型
ollama pull qwen2.5:7b

# 检查模型文件
ls ~/.ollama/models/
```

#### 问题2：内存不足
```python
# 降低批处理大小
BATCH_SIZE = 1  # 减少并发请求

# 使用较小的模型
model = "qwen2.5:7b"  # 而不是14b或32b
```

#### 问题3：响应速度慢
```python
# 优化模型参数
llm = Ollama(
    model="qwen2.5:7b",
    temperature=0.1,
    top_p=0.8,
    top_k=20,  # 减少候选词数量
    num_predict=1000  # 限制生成长度
)
```

### 7.2 性能调优建议

1. **模型选择**: 根据任务复杂度选择合适的模型大小
2. **参数调优**: 调整temperature、top_p等参数
3. **批处理**: 合理设置批处理大小
4. **缓存**: 对常见查询结果进行缓存
5. **负载均衡**: 多实例部署分散负载

---

**文档版本**: v1.0.0  
**适用模型**: Qwen2.5系列  
**最后更新**: 2024年  
**维护状态**: 活跃维护
