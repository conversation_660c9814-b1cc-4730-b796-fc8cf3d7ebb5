# AI合同审查系统 - 架构图表集合

本文档包含AI合同审查系统的所有技术架构图表，使用Mermaid图表语言绘制，可以在支持Mermaid的环境中直接渲染查看。

## 1. 整体架构图

展示系统的完整分层架构，从用户层到基础设施层的9个层次。

```mermaid
graph TB
    %% 用户层
    subgraph "用户层"
        U1[Web浏览器]
        U2[移动端]
        U3[API客户端]
    end
    
    %% 前端层
    subgraph "前端层"
        F1[Vue3 + TypeScript]
        F2[Element Plus UI]
        F3[Pinia状态管理]
        F4[Vue Router路由]
    end
    
    %% API网关层
    subgraph "API网关层"
        G1[Nginx反向代理]
        G2[负载均衡]
        G3[SSL终端]
        G4[CORS处理]
    end
    
    %% 后端服务层
    subgraph "后端服务层"
        B1[FastAPI应用]
        B2[认证中间件]
        B3[异常处理]
        B4[日志记录]
    end
    
    %% 业务服务层
    subgraph "业务服务层"
        S1[文档处理服务]
        S2[AI分析服务]
        S3[风险评估服务]
        S4[合规检查服务]
        S5[报告生成服务]
    end
    
    %% AI引擎层
    subgraph "AI引擎层"
        A1[LangChain框架]
        A2[文本分割器]
        A3[提示模板]
        A4[输出解析器]
    end
    
    %% 模型层
    subgraph "模型层"
        M1[OpenAI GPT-4<br/>开发环境]
        M2[本地LLM<br/>Ollama + Llama3]
        M3[Embedding模型]
    end
    
    %% 数据存储层
    subgraph "数据存储层"
        D1[PostgreSQL<br/>关系数据库]
        D2[Redis<br/>缓存/队列]
        D3[ChromaDB<br/>向量数据库]
        D4[文件系统<br/>文档存储]
    end
    
    %% 基础设施层
    subgraph "基础设施层"
        I1[Docker容器]
        I2[Docker Compose]
        I3[监控系统]
        I4[日志收集]
    end
    
    %% 连接关系
    U1 --> F1
    U2 --> F1
    U3 --> G1
    
    F1 --> F2
    F1 --> F3
    F1 --> F4
    F1 --> G1
    
    G1 --> G2
    G2 --> B1
    G3 --> G1
    G4 --> G1
    
    B1 --> B2
    B1 --> B3
    B1 --> B4
    B1 --> S1
    
    S1 --> S2
    S2 --> S3
    S2 --> S4
    S2 --> S5
    
    S2 --> A1
    A1 --> A2
    A1 --> A3
    A1 --> A4
    
    A1 --> M1
    A1 --> M2
    A1 --> M3
    
    S1 --> D1
    S2 --> D2
    S2 --> D3
    S1 --> D4
    
    B1 --> I1
    I1 --> I2
    I1 --> I3
    I1 --> I4
    
    %% 样式
    classDef userLayer fill:#e1f5fe
    classDef frontendLayer fill:#f3e5f5
    classDef gatewayLayer fill:#e8f5e8
    classDef backendLayer fill:#fff3e0
    classDef serviceLayer fill:#fce4ec
    classDef aiLayer fill:#e0f2f1
    classDef modelLayer fill:#f1f8e9
    classDef dataLayer fill:#e3f2fd
    classDef infraLayer fill:#fafafa
    
    class U1,U2,U3 userLayer
    class F1,F2,F3,F4 frontendLayer
    class G1,G2,G3,G4 gatewayLayer
    class B1,B2,B3,B4 backendLayer
    class S1,S2,S3,S4,S5 serviceLayer
    class A1,A2,A3,A4 aiLayer
    class M1,M2,M3 modelLayer
    class D1,D2,D3,D4 dataLayer
    class I1,I2,I3,I4 infraLayer
```

## 2. 业务流程图

描述从合同上传到分析完成的完整业务流程，包括错误处理机制。

```mermaid
flowchart TD
    A[用户上传合同文件] --> B{文件格式验证}
    B -->|通过| C[保存文件到存储系统]
    B -->|失败| Z1[返回错误信息]
    
    C --> D[创建合同记录]
    D --> E[启动后台处理任务]
    
    E --> F[文档解析模块]
    F --> F1[PDF文本提取]
    F --> F2[DOCX内容解析]
    F --> F3[文本清理标准化]
    F --> F4[结构化信息提取]
    
    F1 --> G[AI智能分析]
    F2 --> G
    F3 --> G
    F4 --> G
    
    G --> G1[文本分割处理]
    G1 --> G2[并行分析任务]
    
    G2 --> H1[合同摘要生成]
    G2 --> H2[关键条款提取]
    G2 --> H3[风险评估分析]
    G2 --> H4[条款逐项分析]
    G2 --> H5[合规性检查]
    G2 --> H6[改进建议生成]
    
    H1 --> I[结果汇总处理]
    H2 --> I
    H3 --> I
    H4 --> I
    H5 --> I
    H6 --> I
    
    I --> J[风险评分计算]
    J --> K[生成分析报告]
    K --> L[更新数据库记录]
    L --> M[发送完成通知]
    
    M --> N[用户查看结果]
    N --> O{用户操作选择}
    
    O -->|查看详细报告| P[展示分析详情]
    O -->|导出报告| Q[生成PDF报告]
    O -->|重新分析| R[重新启动分析流程]
    O -->|删除合同| S[删除文件和记录]
    
    P --> T[风险可视化图表]
    P --> U[条款分析列表]
    P --> V[合规检查结果]
    
    %% 错误处理
    F -->|解析失败| Z2[记录错误日志]
    G -->|分析失败| Z3[回滚状态]
    I -->|处理异常| Z4[错误恢复机制]
    
    Z2 --> Z5[通知用户处理失败]
    Z3 --> Z5
    Z4 --> Z5
    
    %% 样式定义
    classDef startEnd fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef process fill:#e3f2fd,stroke:#2196f3,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef aiProcess fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef error fill:#ffebee,stroke:#f44336,stroke-width:2px
    classDef storage fill:#e0f2f1,stroke:#009688,stroke-width:2px
    
    class A,N startEnd
    class C,D,E,F1,F2,F3,F4,I,J,K,L,M,P,Q,R,S,T,U,V process
    class B,O decision
    class G,G1,G2,H1,H2,H3,H4,H5,H6 aiProcess
    class Z1,Z2,Z3,Z4,Z5 error
    class F storage
```

## 3. 数据库架构图

展示系统的数据库设计，包括主要实体和关系。

```mermaid
erDiagram
    USERS {
        int id PK
        string username
        string email
        string password_hash
        string role
        timestamp created_at
        timestamp updated_at
        boolean is_active
    }
    
    CONTRACTS {
        int id PK
        string title
        string file_name
        string file_path
        int file_size
        string file_type
        string contract_type
        json parties
        date contract_date
        date effective_date
        date expiry_date
        decimal contract_value
        string status
        string overall_risk_level
        float overall_risk_score
        text extracted_text
        json key_terms
        text analysis_summary
        json recommendations
        timestamp created_at
        timestamp updated_at
        timestamp analyzed_at
        int uploaded_by FK
    }
    
    RISK_ASSESSMENTS {
        int id PK
        int contract_id FK
        string risk_category
        string risk_type
        string risk_level
        float risk_score
        text description
        text impact
        float likelihood
        text mitigation
        json related_clauses
        timestamp created_at
    }
    
    CLAUSE_ANALYSES {
        int id PK
        int contract_id FK
        string clause_type
        string clause_title
        text clause_content
        string clause_position
        boolean is_standard
        boolean is_favorable
        float completeness_score
        float clarity_score
        json issues
        json suggestions
        timestamp created_at
    }
    
    COMPLIANCE_CHECKS {
        int id PK
        int contract_id FK
        string regulation_type
        string regulation_name
        string check_result
        float compliance_score
        json requirements
        json violations
        json recommendations
        timestamp created_at
    }
    
    AUDIT_LOGS {
        int id PK
        int user_id FK
        int contract_id FK
        string action
        string resource_type
        json old_values
        json new_values
        string ip_address
        string user_agent
        timestamp created_at
    }
    
    %% 关系定义
    USERS ||--o{ CONTRACTS : uploads
    USERS ||--o{ AUDIT_LOGS : performs
    
    CONTRACTS ||--o{ RISK_ASSESSMENTS : has
    CONTRACTS ||--o{ CLAUSE_ANALYSES : contains
    CONTRACTS ||--o{ COMPLIANCE_CHECKS : requires
    CONTRACTS ||--o{ AUDIT_LOGS : affects
```

## 图表使用说明

### 在线查看
这些Mermaid图表可以在以下环境中查看：
- GitHub/GitLab的Markdown文件中
- Mermaid Live Editor (https://mermaid.live/)
- 支持Mermaid的IDE插件
- 文档生成工具（如GitBook、Docsify等）

### 图表编辑
如需修改图表，请：
1. 复制对应的Mermaid代码
2. 在Mermaid Live Editor中编辑
3. 验证语法正确性
4. 更新到文档中

### 颜色编码说明
- **蓝色系**: 用户界面和前端组件
- **绿色系**: 网关和负载均衡
- **橙色系**: 后端服务和API
- **紫色系**: AI和机器学习组件
- **灰色系**: 基础设施和运维组件
