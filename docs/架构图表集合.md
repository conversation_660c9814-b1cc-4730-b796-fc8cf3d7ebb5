# AI合同审查系统 - 架构图表集合

本文档包含AI合同审查系统的所有技术架构图表，使用Mermaid图表语言绘制，可以在支持Mermaid的环境中直接渲染查看。

## 📋 图表索引

| 序号 | 图表名称 | 主要内容 | 复杂度 |
|------|---------|---------|--------|
| 1 | 整体系统架构图 | 9层分层架构设计 | ⭐⭐⭐ |
| 2 | 业务流程图 | 完整的合同处理流程 | ⭐⭐⭐⭐ |
| 3 | 文档解析架构图 | 8层文档处理架构 | ⭐⭐⭐⭐ |
| 4 | 文档解析数据流图 | 数据在各组件间的流转 | ⭐⭐⭐⭐⭐ |
| 5 | RAG检索增强生成架构图 | RAG技术详细架构 | ⭐⭐⭐⭐⭐ |
| 6 | AI完整分析流程图 | 包含RAG的完整分析流程 | ⭐⭐⭐⭐⭐ |
| 7 | 数据库架构图 | 实体关系设计 | ⭐⭐⭐ |
| 8 | 部署架构图 | 生产环境部署方案 | ⭐⭐⭐⭐ |

## 1. 整体架构图

展示系统的完整分层架构，从用户层到基础设施层的9个层次。

```mermaid
graph TB
    %% 用户层
    subgraph "用户层"
        U1[Web浏览器]
        U2[移动端]
        U3[API客户端]
    end
    
    %% 前端层
    subgraph "前端层"
        F1[Vue3 + TypeScript]
        F2[Element Plus UI]
        F3[Pinia状态管理]
        F4[Vue Router路由]
    end
    
    %% API网关层
    subgraph "API网关层"
        G1[Nginx反向代理]
        G2[负载均衡]
        G3[SSL终端]
        G4[CORS处理]
    end
    
    %% 后端服务层
    subgraph "后端服务层"
        B1[FastAPI应用]
        B2[认证中间件]
        B3[异常处理]
        B4[日志记录]
    end
    
    %% 业务服务层
    subgraph "业务服务层"
        S1[文档处理服务]
        S2[AI分析服务]
        S3[风险评估服务]
        S4[合规检查服务]
        S5[报告生成服务]
    end
    
    %% AI引擎层
    subgraph "AI引擎层"
        A1[LangChain框架]
        A2[文本分割器]
        A3[提示模板]
        A4[输出解析器]
    end
    
    %% 模型层
    subgraph "模型层"
        M1[OpenAI GPT-4<br/>开发环境]
        M2[本地LLM<br/>Ollama + Llama3]
        M3[Embedding模型]
    end
    
    %% 数据存储层
    subgraph "数据存储层"
        D1[PostgreSQL<br/>关系数据库]
        D2[Redis<br/>缓存/队列]
        D3[ChromaDB<br/>向量数据库]
        D4[文件系统<br/>文档存储]
    end
    
    %% 基础设施层
    subgraph "基础设施层"
        I1[Docker容器]
        I2[Docker Compose]
        I3[监控系统]
        I4[日志收集]
    end
    
    %% 连接关系
    U1 --> F1
    U2 --> F1
    U3 --> G1
    
    F1 --> F2
    F1 --> F3
    F1 --> F4
    F1 --> G1
    
    G1 --> G2
    G2 --> B1
    G3 --> G1
    G4 --> G1
    
    B1 --> B2
    B1 --> B3
    B1 --> B4
    B1 --> S1
    
    S1 --> S2
    S2 --> S3
    S2 --> S4
    S2 --> S5
    
    S2 --> A1
    A1 --> A2
    A1 --> A3
    A1 --> A4
    
    A1 --> M1
    A1 --> M2
    A1 --> M3
    
    S1 --> D1
    S2 --> D2
    S2 --> D3
    S1 --> D4
    
    B1 --> I1
    I1 --> I2
    I1 --> I3
    I1 --> I4
    
    %% 样式
    classDef userLayer fill:#e1f5fe
    classDef frontendLayer fill:#f3e5f5
    classDef gatewayLayer fill:#e8f5e8
    classDef backendLayer fill:#fff3e0
    classDef serviceLayer fill:#fce4ec
    classDef aiLayer fill:#e0f2f1
    classDef modelLayer fill:#f1f8e9
    classDef dataLayer fill:#e3f2fd
    classDef infraLayer fill:#fafafa
    
    class U1,U2,U3 userLayer
    class F1,F2,F3,F4 frontendLayer
    class G1,G2,G3,G4 gatewayLayer
    class B1,B2,B3,B4 backendLayer
    class S1,S2,S3,S4,S5 serviceLayer
    class A1,A2,A3,A4 aiLayer
    class M1,M2,M3 modelLayer
    class D1,D2,D3,D4 dataLayer
    class I1,I2,I3,I4 infraLayer
```

## 2. 业务流程图

描述从合同上传到分析完成的完整业务流程，包括错误处理机制。

```mermaid
flowchart TD
    A[用户上传合同文件] --> B{文件格式验证}
    B -->|通过| C[保存文件到存储系统]
    B -->|失败| Z1[返回错误信息]
    
    C --> D[创建合同记录]
    D --> E[启动后台处理任务]
    
    E --> F[文档解析模块]
    F --> F1[PDF文本提取]
    F --> F2[DOCX内容解析]
    F --> F3[文本清理标准化]
    F --> F4[结构化信息提取]
    
    F1 --> G[AI智能分析]
    F2 --> G
    F3 --> G
    F4 --> G
    
    G --> G1[文本分割处理]
    G1 --> G2[并行分析任务]
    
    G2 --> H1[合同摘要生成]
    G2 --> H2[关键条款提取]
    G2 --> H3[风险评估分析]
    G2 --> H4[条款逐项分析]
    G2 --> H5[合规性检查]
    G2 --> H6[改进建议生成]
    
    H1 --> I[结果汇总处理]
    H2 --> I
    H3 --> I
    H4 --> I
    H5 --> I
    H6 --> I
    
    I --> J[风险评分计算]
    J --> K[生成分析报告]
    K --> L[更新数据库记录]
    L --> M[发送完成通知]
    
    M --> N[用户查看结果]
    N --> O{用户操作选择}
    
    O -->|查看详细报告| P[展示分析详情]
    O -->|导出报告| Q[生成PDF报告]
    O -->|重新分析| R[重新启动分析流程]
    O -->|删除合同| S[删除文件和记录]
    
    P --> T[风险可视化图表]
    P --> U[条款分析列表]
    P --> V[合规检查结果]
    
    %% 错误处理
    F -->|解析失败| Z2[记录错误日志]
    G -->|分析失败| Z3[回滚状态]
    I -->|处理异常| Z4[错误恢复机制]
    
    Z2 --> Z5[通知用户处理失败]
    Z3 --> Z5
    Z4 --> Z5
    
    %% 样式定义
    classDef startEnd fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef process fill:#e3f2fd,stroke:#2196f3,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef aiProcess fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    classDef error fill:#ffebee,stroke:#f44336,stroke-width:2px
    classDef storage fill:#e0f2f1,stroke:#009688,stroke-width:2px
    
    class A,N startEnd
    class C,D,E,F1,F2,F3,F4,I,J,K,L,M,P,Q,R,S,T,U,V process
    class B,O decision
    class G,G1,G2,H1,H2,H3,H4,H5,H6 aiProcess
    class Z1,Z2,Z3,Z4,Z5 error
    class F storage
```

## 3. 数据库架构图

展示系统的数据库设计，包括主要实体和关系。

```mermaid
erDiagram
    USERS {
        int id PK
        string username
        string email
        string password_hash
        string role
        timestamp created_at
        timestamp updated_at
        boolean is_active
    }
    
    CONTRACTS {
        int id PK
        string title
        string file_name
        string file_path
        int file_size
        string file_type
        string contract_type
        json parties
        date contract_date
        date effective_date
        date expiry_date
        decimal contract_value
        string status
        string overall_risk_level
        float overall_risk_score
        text extracted_text
        json key_terms
        text analysis_summary
        json recommendations
        timestamp created_at
        timestamp updated_at
        timestamp analyzed_at
        int uploaded_by FK
    }
    
    RISK_ASSESSMENTS {
        int id PK
        int contract_id FK
        string risk_category
        string risk_type
        string risk_level
        float risk_score
        text description
        text impact
        float likelihood
        text mitigation
        json related_clauses
        timestamp created_at
    }
    
    CLAUSE_ANALYSES {
        int id PK
        int contract_id FK
        string clause_type
        string clause_title
        text clause_content
        string clause_position
        boolean is_standard
        boolean is_favorable
        float completeness_score
        float clarity_score
        json issues
        json suggestions
        timestamp created_at
    }
    
    COMPLIANCE_CHECKS {
        int id PK
        int contract_id FK
        string regulation_type
        string regulation_name
        string check_result
        float compliance_score
        json requirements
        json violations
        json recommendations
        timestamp created_at
    }
    
    AUDIT_LOGS {
        int id PK
        int user_id FK
        int contract_id FK
        string action
        string resource_type
        json old_values
        json new_values
        string ip_address
        string user_agent
        timestamp created_at
    }
    
    %% 关系定义
    USERS ||--o{ CONTRACTS : uploads
    USERS ||--o{ AUDIT_LOGS : performs
    
    CONTRACTS ||--o{ RISK_ASSESSMENTS : has
    CONTRACTS ||--o{ CLAUSE_ANALYSES : contains
    CONTRACTS ||--o{ COMPLIANCE_CHECKS : requires
    CONTRACTS ||--o{ AUDIT_LOGS : affects
```

## 3. 文档解析技术架构图

展示完整的8层文档处理架构，从文件输入到向量化存储的全流程。

```mermaid
graph TB
    %% 输入层
    subgraph "文件输入层"
        INPUT[用户上传文件]
        VALIDATE[文件验证器]
        STORAGE[临时存储]
    end

    %% 文件类型识别
    subgraph "文件类型识别"
        DETECTOR[文件类型检测器]
        PDF_CHECK[PDF格式检查]
        DOCX_CHECK[DOCX格式检查]
        DOC_CHECK[DOC格式检查]
        TXT_CHECK[TXT格式检查]
    end

    %% 解析引擎层
    subgraph "解析引擎层"
        subgraph "PDF解析引擎"
            PYMUPDF[PyMuPDF引擎]
            PDFPLUMBER[pdfplumber引擎]
            PDF_MERGER[PDF结果合并器]
        end

        subgraph "Word解析引擎"
            PYTHON_DOCX[python-docx引擎]
            WORD_PARSER[Word结构解析器]
        end

        subgraph "文本解析引擎"
            TXT_READER[文本读取器]
            ENCODING_DETECTOR[编码检测器]
        end
    end

    %% 内容提取层
    subgraph "内容提取层"
        TEXT_EXTRACTOR[文本提取器]
        TABLE_EXTRACTOR[表格提取器]
        IMAGE_EXTRACTOR[图像提取器]
        METADATA_EXTRACTOR[元数据提取器]
        STRUCTURE_ANALYZER[结构分析器]
    end

    %% 文本处理层
    subgraph "文本处理层"
        TEXT_CLEANER[文本清理器]
        TEXT_SPLITTER[文本切分器]
        CHUNK_OPTIMIZER[块优化器]
        OVERLAP_MANAGER[重叠管理器]
    end

    %% 向量化层
    subgraph "向量化层"
        EMBEDDING_SELECTOR[嵌入模型选择器]
        BATCH_PROCESSOR[批处理器]
        VECTOR_VALIDATOR[向量验证器]
    end

    %% 存储层
    subgraph "存储层"
        VECTOR_DB[向量数据库]
        METADATA_STORE[元数据存储]
        INDEX_BUILDER[索引构建器]
    end

    %% 连接关系
    INPUT --> VALIDATE
    VALIDATE --> STORAGE
    STORAGE --> DETECTOR

    DETECTOR --> PDF_CHECK
    DETECTOR --> DOCX_CHECK
    DETECTOR --> DOC_CHECK
    DETECTOR --> TXT_CHECK

    PDF_CHECK --> PYMUPDF
    PDF_CHECK --> PDFPLUMBER
    PYMUPDF --> PDF_MERGER
    PDFPLUMBER --> PDF_MERGER

    DOCX_CHECK --> PYTHON_DOCX
    DOC_CHECK --> PYTHON_DOCX
    PYTHON_DOCX --> WORD_PARSER

    TXT_CHECK --> ENCODING_DETECTOR
    ENCODING_DETECTOR --> TXT_READER

    PDF_MERGER --> TEXT_EXTRACTOR
    WORD_PARSER --> TEXT_EXTRACTOR
    TXT_READER --> TEXT_EXTRACTOR

    TEXT_EXTRACTOR --> TABLE_EXTRACTOR
    TEXT_EXTRACTOR --> IMAGE_EXTRACTOR
    TEXT_EXTRACTOR --> METADATA_EXTRACTOR
    TEXT_EXTRACTOR --> STRUCTURE_ANALYZER

    TABLE_EXTRACTOR --> TEXT_CLEANER
    IMAGE_EXTRACTOR --> TEXT_CLEANER
    METADATA_EXTRACTOR --> TEXT_CLEANER
    STRUCTURE_ANALYZER --> TEXT_CLEANER

    TEXT_CLEANER --> TEXT_SPLITTER
    TEXT_SPLITTER --> CHUNK_OPTIMIZER
    CHUNK_OPTIMIZER --> OVERLAP_MANAGER

    OVERLAP_MANAGER --> EMBEDDING_SELECTOR
    EMBEDDING_SELECTOR --> BATCH_PROCESSOR
    BATCH_PROCESSOR --> VECTOR_VALIDATOR

    VECTOR_VALIDATOR --> VECTOR_DB
    VECTOR_VALIDATOR --> METADATA_STORE
    VECTOR_DB --> INDEX_BUILDER

    %% 样式定义
    classDef input fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef detection fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef parsing fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef extraction fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef processing fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef embedding fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef storage fill:#e1f5fe,stroke:#0277bd,stroke-width:2px

    class INPUT,VALIDATE,STORAGE input
    class DETECTOR,PDF_CHECK,DOCX_CHECK,DOC_CHECK,TXT_CHECK detection
    class PYMUPDF,PDFPLUMBER,PDF_MERGER,PYTHON_DOCX,WORD_PARSER,TXT_READER,ENCODING_DETECTOR parsing
    class TEXT_EXTRACTOR,TABLE_EXTRACTOR,IMAGE_EXTRACTOR,METADATA_EXTRACTOR,STRUCTURE_ANALYZER extraction
    class TEXT_CLEANER,TEXT_SPLITTER,CHUNK_OPTIMIZER,OVERLAP_MANAGER processing
    class EMBEDDING_SELECTOR,BATCH_PROCESSOR,VECTOR_VALIDATOR embedding
    class VECTOR_DB,METADATA_STORE,INDEX_BUILDER storage
```

## 4. 文档解析数据流图

展示数据在文档解析各个组件间的详细流转过程。

```mermaid
flowchart LR
    %% 输入数据
    subgraph "输入数据"
        FILE[原始文件]
        META_IN[文件元信息]
    end

    %% 验证阶段
    subgraph "验证阶段"
        SIZE_CHECK[文件大小检查]
        TYPE_CHECK[文件类型验证]
        SECURITY_CHECK[安全扫描]
        INTEGRITY_CHECK[完整性验证]
    end

    %% 预处理阶段
    subgraph "预处理阶段"
        TEMP_STORE[临时存储]
        BACKUP[备份创建]
        HASH_GEN[文件哈希生成]
    end

    %% 解析阶段
    subgraph "解析阶段"
        subgraph "PDF处理流"
            PDF_TEXT[文本提取]
            PDF_TABLE[表格提取]
            PDF_IMAGE[图像提取]
            PDF_META[元数据提取]
        end

        subgraph "Word处理流"
            WORD_PARA[段落提取]
            WORD_TABLE[表格提取]
            WORD_STYLE[样式信息]
            WORD_META[文档属性]
        end

        subgraph "文本处理流"
            TXT_CONTENT[内容读取]
            TXT_ENCODING[编码转换]
            TXT_FORMAT[格式识别]
        end
    end

    %% 内容整合阶段
    subgraph "内容整合阶段"
        TEXT_MERGE[文本合并]
        TABLE_MERGE[表格整合]
        STRUCT_ANALYSIS[结构分析]
        CONTENT_VALIDATE[内容验证]
    end

    %% 后处理阶段
    subgraph "后处理阶段"
        TEXT_CLEAN[文本清理]
        TEXT_SPLIT[文本切分]
        CHUNK_OPTIMIZE[块优化]
        VECTOR_EMBED[向量化]
    end

    %% 输出数据
    subgraph "输出数据"
        CLEAN_TEXT[清理后文本]
        TEXT_CHUNKS[文本块]
        EMBEDDINGS[向量数据]
        METADATA[文档元数据]
        PARSE_LOG[解析日志]
    end

    %% 数据流连接
    FILE --> SIZE_CHECK
    META_IN --> TYPE_CHECK

    SIZE_CHECK --> TEMP_STORE
    TYPE_CHECK --> SECURITY_CHECK
    SECURITY_CHECK --> INTEGRITY_CHECK
    INTEGRITY_CHECK --> BACKUP

    TEMP_STORE --> HASH_GEN
    BACKUP --> HASH_GEN

    %% 根据文件类型分流
    HASH_GEN -->|PDF| PDF_TEXT
    HASH_GEN -->|PDF| PDF_TABLE
    HASH_GEN -->|PDF| PDF_IMAGE
    HASH_GEN -->|PDF| PDF_META

    HASH_GEN -->|DOCX/DOC| WORD_PARA
    HASH_GEN -->|DOCX/DOC| WORD_TABLE
    HASH_GEN -->|DOCX/DOC| WORD_STYLE
    HASH_GEN -->|DOCX/DOC| WORD_META

    HASH_GEN -->|TXT| TXT_CONTENT
    HASH_GEN -->|TXT| TXT_ENCODING
    HASH_GEN -->|TXT| TXT_FORMAT

    %% 内容整合
    PDF_TEXT --> TEXT_MERGE
    WORD_PARA --> TEXT_MERGE
    TXT_CONTENT --> TEXT_MERGE

    PDF_TABLE --> TABLE_MERGE
    WORD_TABLE --> TABLE_MERGE

    PDF_META --> STRUCT_ANALYSIS
    WORD_STYLE --> STRUCT_ANALYSIS
    TXT_FORMAT --> STRUCT_ANALYSIS

    TEXT_MERGE --> CONTENT_VALIDATE
    TABLE_MERGE --> CONTENT_VALIDATE
    STRUCT_ANALYSIS --> CONTENT_VALIDATE

    %% 后处理流程
    CONTENT_VALIDATE --> TEXT_CLEAN
    TEXT_CLEAN --> TEXT_SPLIT
    TEXT_SPLIT --> CHUNK_OPTIMIZE
    CHUNK_OPTIMIZE --> VECTOR_EMBED

    %% 输出生成
    VECTOR_EMBED --> CLEAN_TEXT
    VECTOR_EMBED --> TEXT_CHUNKS
    VECTOR_EMBED --> EMBEDDINGS
    STRUCT_ANALYSIS --> METADATA
    CONTENT_VALIDATE --> PARSE_LOG

    %% 样式定义
    classDef input fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef validation fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef preprocessing fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef parsing fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef integration fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef postprocessing fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef output fill:#e1f5fe,stroke:#0277bd,stroke-width:2px

    class FILE,META_IN input
    class SIZE_CHECK,TYPE_CHECK,SECURITY_CHECK,INTEGRITY_CHECK validation
    class TEMP_STORE,BACKUP,HASH_GEN preprocessing
    class PDF_TEXT,PDF_TABLE,PDF_IMAGE,PDF_META,WORD_PARA,WORD_TABLE,WORD_STYLE,WORD_META,TXT_CONTENT,TXT_ENCODING,TXT_FORMAT parsing
    class TEXT_MERGE,TABLE_MERGE,STRUCT_ANALYSIS,CONTENT_VALIDATE integration
    class TEXT_CLEAN,TEXT_SPLIT,CHUNK_OPTIMIZE,VECTOR_EMBED postprocessing
    class CLEAN_TEXT,TEXT_CHUNKS,EMBEDDINGS,METADATA,PARSE_LOG output
```

## 图表使用说明

### 在线查看
这些Mermaid图表可以在以下环境中查看：
- GitHub/GitLab的Markdown文件中
- Mermaid Live Editor (https://mermaid.live/)
- 支持Mermaid的IDE插件
- 文档生成工具（如GitBook、Docsify等）

### 图表编辑
如需修改图表，请：
1. 复制对应的Mermaid代码
2. 在Mermaid Live Editor中编辑
3. 验证语法正确性
4. 更新到文档中

## 5. RAG检索增强生成架构图

展示完整的RAG技术架构，包含知识库构建、检索引擎、上下文构建和增强生成。

```mermaid
graph TB
    %% 知识库构建
    subgraph "知识库构建"
        DOC_CORPUS[文档语料库]
        LEGAL_KB[法律知识库]
        CONTRACT_TEMPLATES[合同模板库]
        CASE_DB[案例数据库]
        REGULATION_DB[法规数据库]
    end

    %% 向量化存储
    subgraph "向量化存储"
        CHUNK_STORE[文本块存储]
        VECTOR_DB[向量数据库]
        METADATA_STORE[元数据存储]
        INDEX_ENGINE[索引引擎]
    end

    %% 查询处理
    subgraph "查询处理"
        QUERY_INPUT[用户查询/合同文本]
        QUERY_PROCESSOR[查询预处理器]
        QUERY_EXPANSION[查询扩展器]
        QUERY_EMBEDDING[查询向量化]
    end

    %% 检索引擎
    subgraph "检索引擎"
        SIMILARITY_SEARCH[相似度搜索]
        HYBRID_SEARCH[混合搜索]
        RERANKER[重排序器]
        CONTEXT_FILTER[上下文过滤器]
    end

    %% 上下文构建
    subgraph "上下文构建"
        CONTEXT_BUILDER[上下文构建器]
        RELEVANCE_SCORER[相关性评分器]
        CONTEXT_OPTIMIZER[上下文优化器]
        TOKEN_MANAGER[Token管理器]
    end

    %% 增强生成
    subgraph "增强生成"
        PROMPT_BUILDER[提示构建器]
        LLM_ENGINE[大语言模型]
        RESPONSE_VALIDATOR[响应验证器]
        HALLUCINATION_DETECTOR[幻觉检测器]
    end

    %% 后处理
    subgraph "后处理"
        ANSWER_EXTRACTOR[答案提取器]
        CITATION_GENERATOR[引用生成器]
        CONFIDENCE_SCORER[置信度评分器]
        QUALITY_CHECKER[质量检查器]
    end

    %% 反馈循环
    subgraph "反馈优化"
        USER_FEEDBACK[用户反馈]
        PERFORMANCE_MONITOR[性能监控]
        MODEL_UPDATER[模型更新器]
        KNOWLEDGE_UPDATER[知识库更新器]
    end

    %% 流程连接
    DOC_CORPUS --> CHUNK_STORE
    LEGAL_KB --> CHUNK_STORE
    CONTRACT_TEMPLATES --> CHUNK_STORE
    CASE_DB --> CHUNK_STORE
    REGULATION_DB --> CHUNK_STORE

    CHUNK_STORE --> VECTOR_DB
    CHUNK_STORE --> METADATA_STORE
    VECTOR_DB --> INDEX_ENGINE

    QUERY_INPUT --> QUERY_PROCESSOR
    QUERY_PROCESSOR --> QUERY_EXPANSION
    QUERY_EXPANSION --> QUERY_EMBEDDING

    QUERY_EMBEDDING --> SIMILARITY_SEARCH
    QUERY_EMBEDDING --> HYBRID_SEARCH
    INDEX_ENGINE --> SIMILARITY_SEARCH
    INDEX_ENGINE --> HYBRID_SEARCH

    SIMILARITY_SEARCH --> RERANKER
    HYBRID_SEARCH --> RERANKER
    RERANKER --> CONTEXT_FILTER

    CONTEXT_FILTER --> CONTEXT_BUILDER
    METADATA_STORE --> CONTEXT_BUILDER
    CONTEXT_BUILDER --> RELEVANCE_SCORER
    RELEVANCE_SCORER --> CONTEXT_OPTIMIZER
    CONTEXT_OPTIMIZER --> TOKEN_MANAGER

    TOKEN_MANAGER --> PROMPT_BUILDER
    QUERY_INPUT --> PROMPT_BUILDER
    PROMPT_BUILDER --> LLM_ENGINE
    LLM_ENGINE --> RESPONSE_VALIDATOR
    RESPONSE_VALIDATOR --> HALLUCINATION_DETECTOR

    HALLUCINATION_DETECTOR --> ANSWER_EXTRACTOR
    ANSWER_EXTRACTOR --> CITATION_GENERATOR
    CITATION_GENERATOR --> CONFIDENCE_SCORER
    CONFIDENCE_SCORER --> QUALITY_CHECKER

    QUALITY_CHECKER --> USER_FEEDBACK
    USER_FEEDBACK --> PERFORMANCE_MONITOR
    PERFORMANCE_MONITOR --> MODEL_UPDATER
    PERFORMANCE_MONITOR --> KNOWLEDGE_UPDATER

    MODEL_UPDATER --> LLM_ENGINE
    KNOWLEDGE_UPDATER --> CHUNK_STORE

    %% 样式定义
    classDef knowledge fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef storage fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef query fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef retrieval fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef context fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef generation fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef postprocess fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef feedback fill:#f5f5f5,stroke:#616161,stroke-width:2px

    class DOC_CORPUS,LEGAL_KB,CONTRACT_TEMPLATES,CASE_DB,REGULATION_DB knowledge
    class CHUNK_STORE,VECTOR_DB,METADATA_STORE,INDEX_ENGINE storage
    class QUERY_INPUT,QUERY_PROCESSOR,QUERY_EXPANSION,QUERY_EMBEDDING query
    class SIMILARITY_SEARCH,HYBRID_SEARCH,RERANKER,CONTEXT_FILTER retrieval
    class CONTEXT_BUILDER,RELEVANCE_SCORER,CONTEXT_OPTIMIZER,TOKEN_MANAGER context
    class PROMPT_BUILDER,LLM_ENGINE,RESPONSE_VALIDATOR,HALLUCINATION_DETECTOR generation
    class ANSWER_EXTRACTOR,CITATION_GENERATOR,CONFIDENCE_SCORER,QUALITY_CHECKER postprocess
    class USER_FEEDBACK,PERFORMANCE_MONITOR,MODEL_UPDATER,KNOWLEDGE_UPDATER feedback
```

### 颜色编码说明
- **蓝色系**: 知识库和输入组件
- **绿色系**: 存储和索引组件
- **橙色系**: 查询处理组件
- **紫色系**: 检索和排序组件
- **红色系**: 上下文构建组件
- **青色系**: 生成和验证组件
- **浅蓝系**: 后处理组件
- **灰色系**: 反馈和优化组件

## 6. AI完整分析流程图（包含RAG）

展示从文档输入到最终报告生成的完整AI分析流程，突出RAG架构的作用。

```mermaid
flowchart TD
    %% 文档输入
    subgraph "文档输入阶段"
        DOC_UPLOAD[合同文档上传]
        FILE_VALIDATE[文件验证]
        DOC_PARSE[文档解析]
        TEXT_EXTRACT[文本提取]
    end

    %% 文本处理
    subgraph "文本处理阶段"
        TEXT_CLEAN[文本清理]
        TEXT_SPLIT[文本切分]
        CHUNK_OPTIMIZE[块优化]
        TEXT_EMBED[文本向量化]
    end

    %% 知识库检索
    subgraph "知识库检索阶段"
        QUERY_PROCESS[查询处理]
        KNOWLEDGE_RETRIEVE[知识检索]
        CONTEXT_BUILD[上下文构建]
        RELEVANCE_RANK[相关性排序]
    end

    %% AI分析任务
    subgraph "AI分析任务"
        subgraph "摘要生成"
            SUMMARY_QUERY[摘要查询构建]
            SUMMARY_RETRIEVE[相关内容检索]
            SUMMARY_GENERATE[摘要生成]
        end

        subgraph "风险评估"
            RISK_QUERY[风险查询构建]
            RISK_RETRIEVE[风险知识检索]
            RISK_ANALYZE[风险分析]
        end

        subgraph "条款分析"
            CLAUSE_QUERY[条款查询构建]
            CLAUSE_RETRIEVE[条款知识检索]
            CLAUSE_ANALYZE[条款分析]
        end

        subgraph "合规检查"
            COMPLIANCE_QUERY[合规查询构建]
            COMPLIANCE_RETRIEVE[法规知识检索]
            COMPLIANCE_CHECK[合规检查]
        end
    end

    %% 质量控制
    subgraph "质量控制阶段"
        HALLUCINATION_DETECT[幻觉检测]
        FACT_CHECK[事实检查]
        CONSISTENCY_CHECK[一致性检查]
        CITATION_GEN[引用生成]
    end

    %% 结果整合
    subgraph "结果整合阶段"
        RESULT_MERGE[结果合并]
        CONFIDENCE_SCORE[置信度评分]
        REPORT_GENERATE[报告生成]
        QUALITY_VALIDATE[质量验证]
    end

    %% 知识库
    subgraph "知识库"
        LEGAL_KB[法律知识库]
        CONTRACT_TEMPLATES[合同模板库]
        CASE_DB[案例数据库]
        REGULATION_DB[法规数据库]
        VECTOR_DB[向量数据库]
    end

    %% 流程连接
    DOC_UPLOAD --> FILE_VALIDATE
    FILE_VALIDATE --> DOC_PARSE
    DOC_PARSE --> TEXT_EXTRACT

    TEXT_EXTRACT --> TEXT_CLEAN
    TEXT_CLEAN --> TEXT_SPLIT
    TEXT_SPLIT --> CHUNK_OPTIMIZE
    CHUNK_OPTIMIZE --> TEXT_EMBED

    TEXT_EMBED --> VECTOR_DB

    %% 并行分析任务
    TEXT_EXTRACT --> SUMMARY_QUERY
    TEXT_EXTRACT --> RISK_QUERY
    TEXT_EXTRACT --> CLAUSE_QUERY
    TEXT_EXTRACT --> COMPLIANCE_QUERY

    %% 摘要生成流程
    SUMMARY_QUERY --> QUERY_PROCESS
    QUERY_PROCESS --> KNOWLEDGE_RETRIEVE
    KNOWLEDGE_RETRIEVE --> LEGAL_KB
    KNOWLEDGE_RETRIEVE --> CONTRACT_TEMPLATES
    KNOWLEDGE_RETRIEVE --> VECTOR_DB

    KNOWLEDGE_RETRIEVE --> CONTEXT_BUILD
    CONTEXT_BUILD --> RELEVANCE_RANK
    RELEVANCE_RANK --> SUMMARY_RETRIEVE
    SUMMARY_RETRIEVE --> SUMMARY_GENERATE

    %% 风险评估流程
    RISK_QUERY --> KNOWLEDGE_RETRIEVE
    KNOWLEDGE_RETRIEVE --> CASE_DB
    RELEVANCE_RANK --> RISK_RETRIEVE
    RISK_RETRIEVE --> RISK_ANALYZE

    %% 条款分析流程
    CLAUSE_QUERY --> KNOWLEDGE_RETRIEVE
    RELEVANCE_RANK --> CLAUSE_RETRIEVE
    CLAUSE_RETRIEVE --> CLAUSE_ANALYZE

    %% 合规检查流程
    COMPLIANCE_QUERY --> KNOWLEDGE_RETRIEVE
    KNOWLEDGE_RETRIEVE --> REGULATION_DB
    RELEVANCE_RANK --> COMPLIANCE_RETRIEVE
    COMPLIANCE_RETRIEVE --> COMPLIANCE_CHECK

    %% 质量控制
    SUMMARY_GENERATE --> HALLUCINATION_DETECT
    RISK_ANALYZE --> HALLUCINATION_DETECT
    CLAUSE_ANALYZE --> HALLUCINATION_DETECT
    COMPLIANCE_CHECK --> HALLUCINATION_DETECT

    HALLUCINATION_DETECT --> FACT_CHECK
    FACT_CHECK --> CONSISTENCY_CHECK
    CONSISTENCY_CHECK --> CITATION_GEN

    %% 结果整合
    CITATION_GEN --> RESULT_MERGE
    RESULT_MERGE --> CONFIDENCE_SCORE
    CONFIDENCE_SCORE --> REPORT_GENERATE
    REPORT_GENERATE --> QUALITY_VALIDATE

    %% 样式定义
    classDef input fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef processing fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef retrieval fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef analysis fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef quality fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef integration fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef knowledge fill:#e1f5fe,stroke:#0277bd,stroke-width:2px

    class DOC_UPLOAD,FILE_VALIDATE,DOC_PARSE,TEXT_EXTRACT input
    class TEXT_CLEAN,TEXT_SPLIT,CHUNK_OPTIMIZE,TEXT_EMBED processing
    class QUERY_PROCESS,KNOWLEDGE_RETRIEVE,CONTEXT_BUILD,RELEVANCE_RANK retrieval
    class SUMMARY_QUERY,SUMMARY_RETRIEVE,SUMMARY_GENERATE,RISK_QUERY,RISK_RETRIEVE,RISK_ANALYZE,CLAUSE_QUERY,CLAUSE_RETRIEVE,CLAUSE_ANALYZE,COMPLIANCE_QUERY,COMPLIANCE_RETRIEVE,COMPLIANCE_CHECK analysis
    class HALLUCINATION_DETECT,FACT_CHECK,CONSISTENCY_CHECK,CITATION_GEN quality
    class RESULT_MERGE,CONFIDENCE_SCORE,REPORT_GENERATE,QUALITY_VALIDATE integration
    class LEGAL_KB,CONTRACT_TEMPLATES,CASE_DB,REGULATION_DB,VECTOR_DB knowledge
```

## 7. 部署架构图

展示生产环境的高可用部署架构，支持负载均衡和集群扩展。

```mermaid
graph TB
    %% 外部用户
    subgraph "外部访问"
        U1[Web用户]
        U2[移动用户]
        U3[API客户端]
    end

    %% 负载均衡层
    subgraph "负载均衡层"
        LB[Nginx负载均衡器]
        SSL[SSL证书]
    end

    %% 应用层集群
    subgraph "应用服务集群"
        subgraph "前端服务"
            F1[Vue应用实例1]
            F2[Vue应用实例2]
            F3[Vue应用实例N]
        end

        subgraph "后端服务"
            B1[FastAPI实例1]
            B2[FastAPI实例2]
            B3[FastAPI实例N]
        end
    end

    %% 数据层集群
    subgraph "数据存储集群"
        subgraph "关系数据库"
            PG1[PostgreSQL主库]
            PG2[PostgreSQL从库1]
            PG3[PostgreSQL从库2]
        end

        subgraph "缓存集群"
            R1[Redis主节点]
            R2[Redis从节点1]
            R3[Redis从节点2]
        end

        subgraph "向量数据库"
            C1[ChromaDB实例1]
            C2[ChromaDB实例2]
        end

        subgraph "文件存储"
            FS1[文件存储卷1]
            FS2[文件存储卷2]
            NFS[NFS共享存储]
        end
    end

    %% AI服务层
    subgraph "AI服务层"
        subgraph "开发环境"
            AI1[OpenAI API]
            AI2[Azure OpenAI]
        end

        subgraph "生产环境"
            LOCAL1[Ollama实例1]
            LOCAL2[Ollama实例2]
            GPU1[GPU服务器1]
            GPU2[GPU服务器2]
        end
    end

    %% 监控和日志
    subgraph "监控运维"
        MON1[Prometheus监控]
        MON2[Grafana仪表板]
        LOG1[ELK日志栈]
        ALERT[告警系统]
    end

    %% 连接关系
    U1 --> LB
    U2 --> LB
    U3 --> LB

    LB --> SSL
    LB --> F1
    LB --> F2
    LB --> F3

    F1 --> B1
    F2 --> B2
    F3 --> B3

    B1 --> PG1
    B2 --> PG1
    B3 --> PG1

    PG1 --> PG2
    PG1 --> PG3

    B1 --> R1
    B2 --> R1
    B3 --> R1

    R1 --> R2
    R1 --> R3

    B1 --> C1
    B2 --> C2
    B3 --> C1

    B1 --> FS1
    B2 --> FS2
    B3 --> NFS

    B1 --> AI1
    B2 --> LOCAL1
    B3 --> LOCAL2

    LOCAL1 --> GPU1
    LOCAL2 --> GPU2

    B1 --> MON1
    B2 --> MON1
    B3 --> MON1

    MON1 --> MON2
    MON1 --> ALERT

    B1 --> LOG1
    B2 --> LOG1
    B3 --> LOG1

    %% 样式定义
    classDef external fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef loadbalancer fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef application fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef database fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef ai fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef monitoring fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    class U1,U2,U3 external
    class LB,SSL loadbalancer
    class F1,F2,F3,B1,B2,B3 application
    class PG1,PG2,PG3,R1,R2,R3,C1,C2,FS1,FS2,NFS database
    class AI1,AI2,LOCAL1,LOCAL2,GPU1,GPU2 ai
    class MON1,MON2,LOG1,ALERT monitoring
```

## 8. 架构图表总结

### 图表关系说明

1. **整体系统架构图** → 提供系统全貌，展示各层次关系
2. **业务流程图** → 描述用户操作到结果输出的完整流程
3. **文档解析架构图** → 详细展示文档处理的8层架构
4. **文档解析数据流图** → 展示数据在解析过程中的流转
5. **RAG检索增强生成架构图** → 核心AI技术架构
6. **AI完整分析流程图** → 整合RAG的完整分析流程
7. **数据库架构图** → 数据存储和关系设计
8. **部署架构图** → 生产环境部署方案

### 技术架构特点

#### 核心优势
- **RAG架构**: 避免AI幻觉，基于真实知识库生成
- **8层处理**: 完整的文档到向量化流水线
- **质量控制**: 多层验证和幻觉检测机制
- **高可用**: 集群部署和负载均衡
- **可扩展**: 模块化设计，支持水平扩展

#### 性能指标
- 文档处理: < 30秒 (10MB PDF)
- AI分析: < 60秒响应时间
- 系统可用性: > 99.5%
- 并发支持: 1000+用户

#### 安全特性
- 本地化部署支持
- 数据加密存储
- 完整审计日志
- 多层访问控制

### 图表维护说明

#### 版本控制
- 所有图表使用Mermaid语法
- 支持Git版本控制
- 便于团队协作编辑
- 自动渲染和预览

#### 更新流程
1. 在Mermaid Live Editor中编辑
2. 验证语法正确性
3. 更新到对应文档
4. 提交版本控制
5. 通知相关团队

#### 使用建议
- 技术方案评审时使用
- 新团队成员培训材料
- 系统维护和升级参考
- 对外技术交流展示

---

**图表集合版本**: v1.0.0
**包含图表数量**: 8个核心架构图
**最后更新**: 2024年
**维护状态**: 活跃维护
