# AI合同审查系统

一个基于大语言模型的企业级智能合同审查平台，提供自动化的合同分析、风险评估和合规检查服务。

## 🚀 项目特性

- **智能文档解析**: 支持PDF、DOCX、DOC、TXT等多种格式
- **AI驱动分析**: 基于LLM的深度合同理解和分析
- **多维风险评估**: 法律、商业、操作、财务、合规等全方位风险识别
- **合规性检查**: 自动检查合同条款的法律法规符合性
- **专业报告生成**: 生成详细的分析报告和改进建议
- **企业级架构**: 支持高并发、可扩展、安全可靠

## 🏗️ 技术架构

### 后端技术栈
- **框架**: Python 3.11 + FastAPI
- **AI引擎**: LangChain + LangGraph
- **文档处理**: PyMuPDF, python-docx, Unstructured
- **数据库**: PostgreSQL + Redis + ChromaDB
- **容器化**: Docker + Docker Compose

### 前端技术栈
- **框架**: Vue 3 + TypeScript
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **构建工具**: Vite

### AI模型支持
- **开发环境**: OpenAI GPT-4 (远程API)
- **生产环境**: 本地部署模型 (Ollama + Llama3/Qwen)

## 📁 项目结构

```
testpy/
├── backend/                    # 后端服务
│   ├── app/
│   │   ├── api/               # API路由
│   │   │   └── v1/
│   │   │       └── endpoints/
│   │   ├── core/              # 核心配置
│   │   ├── models/            # 数据模型
│   │   ├── schemas/           # Pydantic模式
│   │   ├── services/          # 业务服务
│   │   │   ├── document_processor.py  # 文档处理
│   │   │   ├── ai_analyzer.py         # AI分析
│   │   │   └── risk_assessor.py       # 风险评估
│   │   └── main.py            # 应用入口
│   ├── alembic/               # 数据库迁移
│   ├── Dockerfile
│   └── requirements.txt
├── frontend/                   # 前端应用
│   ├── src/
│   │   ├── components/        # Vue组件
│   │   ├── views/             # 页面视图
│   │   ├── stores/            # Pinia状态管理
│   │   ├── router/            # 路由配置
│   │   ├── api/               # API调用
│   │   └── main.ts            # 应用入口
│   ├── Dockerfile
│   └── package.json
├── docs/                       # 项目文档
│   ├── 技术架构设计.md
│   ├── 技术选型与实施指南.md
│   └── API文档.md
├── database/                   # 数据库脚本
├── docker-compose.yml          # Docker编排
└── README.md
```

## 🚀 快速开始

### 环境要求
- Python 3.11+
- Node.js 18+
- Docker 24+
- PostgreSQL 15+
- Redis 7+

### 1. 克隆项目
```bash
git clone <repository-url>
cd testpy
```

### 2. 环境配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
# 配置数据库连接、OpenAI API Key等
```

### 3. Docker一键启动
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 4. 手动启动（开发模式）

#### 后端启动
```bash
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# 安装依赖
pip install -r requirements.txt

# 数据库迁移
alembic upgrade head

# 启动服务
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 前端启动
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 5. 访问应用
- 前端应用: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

## 🔧 配置说明

### 环境变量配置
```bash
# 数据库配置
DATABASE_URL=postgresql://contract_user:contract_pass@localhost:5432/contract_db
REDIS_URL=redis://localhost:6379

# AI模型配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_BASE_URL=https://api.openai.com/v1  # 可选，支持自定义端点

# 应用配置
SECRET_KEY=your-secret-key
ENVIRONMENT=development
DEBUG=true

# 文件上传配置
MAX_FILE_SIZE=52428800  # 50MB
UPLOAD_DIR=uploads
```

### 数据库初始化
```bash
# 创建数据库
createdb contract_db

# 运行迁移
cd backend
alembic upgrade head
```

## 📖 使用指南

### 1. 上传合同
- 支持PDF、DOCX、DOC、TXT格式
- 文件大小限制：50MB
- 自动提取文本内容和元数据

### 2. 智能分析
系统会自动进行以下分析：
- **合同摘要**: 生成合同的核心内容摘要
- **关键条款提取**: 识别重要的合同条款
- **风险评估**: 多维度风险分析和评级
- **条款分析**: 逐条分析合同条款的完整性和清晰度
- **合规检查**: 检查合同的法律法规符合性
- **改进建议**: 提供专业的合同优化建议

### 3. 查看报告
- 详细的分析报告
- 风险评估结果
- 可视化图表展示
- 导出PDF报告

## 🔒 安全特性

- **文件安全**: 文件类型验证、大小限制、安全存储
- **数据加密**: 敏感数据加密存储
- **访问控制**: JWT认证、角色权限管理
- **本地部署**: 支持完全本地化部署，数据不出境
- **审计日志**: 完整的操作日志记录

## 📊 性能优化

- **异步处理**: 文档处理和AI分析异步化
- **缓存策略**: Redis缓存分析结果
- **数据库优化**: 索引优化、查询优化
- **并发处理**: 支持多文档并行处理

## 🛠️ 开发指南

### 代码规范
```bash
# 后端代码格式化
cd backend
black app/
isort app/
flake8 app/

# 前端代码格式化
cd frontend
npm run lint
npm run format
```

### 运行测试
```bash
# 后端测试
cd backend
pytest

# 前端测试
cd frontend
npm run test
```

### 数据库迁移
```bash
# 创建新迁移
alembic revision --autogenerate -m "描述"

# 应用迁移
alembic upgrade head

# 回滚迁移
alembic downgrade -1
```

## 📈 监控与运维

### 健康检查
```bash
# API健康检查
curl http://localhost:8000/health

# 数据库连接检查
curl http://localhost:8000/api/v1/health
```

### 日志查看
```bash
# 查看应用日志
docker-compose logs -f api

# 查看数据库日志
docker-compose logs -f postgres
```

### 性能监控
- 应用性能指标
- 数据库性能监控
- 资源使用情况
- 错误率统计

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 支持与反馈

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件
- 技术交流群

## 🗺️ 路线图

- [ ] 支持更多文档格式
- [ ] 多语言合同分析
- [ ] 合同模板库
- [ ] 批量处理功能
- [ ] 移动端支持
- [ ] 第三方系统集成
- [ ] 高级分析功能
- [ ] 机器学习模型优化
